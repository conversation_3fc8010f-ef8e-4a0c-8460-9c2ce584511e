<?php
/**
 * Test Withdrawal System Setup
 * 
 * This script checks if all required components for the withdrawal system are in place
 */

require_once 'config/config.php';
require_once 'config/db.php';

echo "<h2>🔍 Withdrawal System Setup Check</h2>";

// Check if required tables exist
$tables = [
    'shop_owner_earnings',
    'payment_withdrawal_requests', 
    'payment_withdrawal_status_history',
    'payment_method_settings',
    'shop_owners',
    'laundry_shops'
];

echo "<h3>📋 Database Tables</h3>";
$missingTables = [];

foreach ($tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
            $missingTables[] = $table;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error checking table '$table': " . htmlspecialchars($e->getMessage()) . "</p>";
        $missingTables[] = $table;
    }
}

// Check if PaymentWithdrawalManager class exists
echo "<h3>📦 Required Classes</h3>";
try {
    require_once 'includes/PaymentWithdrawalManager.php';
    if (class_exists('PaymentWithdrawalManager')) {
        echo "<p style='color: green;'>✓ PaymentWithdrawalManager class exists</p>";
    } else {
        echo "<p style='color: red;'>✗ PaymentWithdrawalManager class missing</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error loading PaymentWithdrawalManager: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check if withdrawal page exists
echo "<h3>📄 Required Files</h3>";
$files = [
    'laundryshop/withdrawal_requests.php',
    'includes/PaymentWithdrawalManager.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ File '$file' exists</p>";
    } else {
        echo "<p style='color: red;'>✗ File '$file' missing</p>";
    }
}

// If tables are missing, provide creation SQL
if (!empty($missingTables)) {
    echo "<h3>🔧 Missing Tables - Creation SQL</h3>";
    echo "<p>The following SQL can be used to create the missing tables:</p>";
    echo "<textarea style='width: 100%; height: 300px; font-family: monospace;'>";
    
    if (in_array('shop_owner_earnings', $missingTables)) {
        echo "-- Shop Owner Earnings Table
CREATE TABLE IF NOT EXISTS `shop_owner_earnings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `shop_id` int(11) NOT NULL,
    `shop_owner_id` int(11) NOT NULL,
    `total_earnings` decimal(12,2) NOT NULL DEFAULT 0.00,
    `withdrawn_amount` decimal(12,2) NOT NULL DEFAULT 0.00,
    `pending_withdrawal` decimal(12,2) NOT NULL DEFAULT 0.00,
    `available_balance` decimal(12,2) NOT NULL DEFAULT 0.00,
    `last_withdrawal_date` timestamp NULL DEFAULT NULL,
    `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_shop_owner` (`shop_id`, `shop_owner_id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_shop_owner_id` (`shop_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

";
    }
    
    if (in_array('payment_withdrawal_requests', $missingTables)) {
        echo "-- Payment Withdrawal Requests Table
CREATE TABLE IF NOT EXISTS `payment_withdrawal_requests` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `shop_id` int(11) NOT NULL,
    `shop_owner_id` int(11) NOT NULL,
    `request_amount` decimal(10,2) NOT NULL,
    `available_balance` decimal(10,2) NOT NULL,
    `payment_method` enum('bkash', 'nagad', 'rocket', 'bank_transfer') NOT NULL,
    `account_type` enum('personal', 'agent', 'merchant') NOT NULL DEFAULT 'personal',
    `account_number` varchar(20) NOT NULL,
    `account_holder_name` varchar(100) NOT NULL,
    `shop_owner_notes` text DEFAULT NULL,
    `status` enum('pending', 'processing', 'approved', 'completed', 'rejected', 'cancelled') NOT NULL DEFAULT 'pending',
    `admin_id` int(11) DEFAULT NULL,
    `admin_notes` text DEFAULT NULL,
    `rejection_reason` text DEFAULT NULL,
    `transaction_reference` varchar(100) DEFAULT NULL,
    `processing_fee` decimal(8,2) DEFAULT 0.00,
    `final_amount` decimal(10,2) DEFAULT NULL,
    `processed_at` timestamp NULL DEFAULT NULL,
    `completed_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_shop_owner_id` (`shop_owner_id`),
    KEY `idx_status` (`status`),
    KEY `idx_payment_method` (`payment_method`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

";
    }
    
    if (in_array('payment_withdrawal_status_history', $missingTables)) {
        echo "-- Payment Withdrawal Status History Table
CREATE TABLE IF NOT EXISTS `payment_withdrawal_status_history` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `withdrawal_request_id` int(11) NOT NULL,
    `old_status` enum('pending', 'processing', 'approved', 'completed', 'rejected', 'cancelled') DEFAULT NULL,
    `new_status` enum('pending', 'processing', 'approved', 'completed', 'rejected', 'cancelled') NOT NULL,
    `changed_by` int(11) DEFAULT NULL,
    `change_reason` text DEFAULT NULL,
    `admin_notes` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_withdrawal_request_id` (`withdrawal_request_id`),
    KEY `idx_changed_by` (`changed_by`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

";
    }
    
    if (in_array('payment_method_settings', $missingTables)) {
        echo "-- Payment Method Settings Table
CREATE TABLE IF NOT EXISTS `payment_method_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `method_name` varchar(50) NOT NULL,
    `display_name` varchar(100) NOT NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `min_amount` decimal(8,2) NOT NULL DEFAULT 0.00,
    `max_amount` decimal(10,2) NOT NULL DEFAULT 999999.99,
    `processing_fee_type` enum('fixed', 'percentage') NOT NULL DEFAULT 'percentage',
    `processing_fee_value` decimal(8,4) NOT NULL DEFAULT 0.00,
    `account_number_pattern` varchar(100) DEFAULT NULL,
    `account_number_example` varchar(50) DEFAULT NULL,
    `instructions` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_method_name` (`method_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default payment method settings
INSERT INTO `payment_method_settings` (`method_name`, `display_name`, `min_amount`, `max_amount`, `processing_fee_type`, `processing_fee_value`, `account_number_pattern`, `account_number_example`, `instructions`) VALUES
('bkash', 'bKash', 50.00, 25000.00, 'percentage', 1.85, '^01[3-9][0-9]{8}$', '***********', 'Enter your bKash account number (11 digits starting with 01)'),
('nagad', 'Nagad', 50.00, 25000.00, 'percentage', 1.99, '^01[3-9][0-9]{8}$', '***********', 'Enter your Nagad account number (11 digits starting with 01)'),
('rocket', 'Rocket', 50.00, 20000.00, 'percentage', 1.80, '^01[3-9][0-9]{8}$', '***********', 'Enter your Rocket account number (11 digits starting with 01)'),
('bank_transfer', 'Bank Transfer', 500.00, 100000.00, 'fixed', 25.00, NULL, 'Account Number', 'Bank transfers may take 1-3 business days to process');

";
    }
    
    echo "</textarea>";
    
    echo "<h3>🚀 Quick Setup</h3>";
    echo "<p><a href='#' onclick='createTables()' class='btn btn-primary'>Create Missing Tables</a></p>";
    echo "<div id='createResult'></div>";
}

// Test basic functionality if all components exist
if (empty($missingTables) && class_exists('PaymentWithdrawalManager')) {
    echo "<h3>🧪 Functionality Test</h3>";
    try {
        $paymentWithdrawalManager = new PaymentWithdrawalManager($pdo);
        $paymentMethods = $paymentWithdrawalManager->getPaymentMethodSettings();
        
        echo "<p style='color: green;'>✓ PaymentWithdrawalManager initialized successfully</p>";
        echo "<p style='color: green;'>✓ Found " . count($paymentMethods) . " payment methods configured</p>";
        
        foreach ($paymentMethods as $method) {
            $status = $method['is_active'] ? 'Active' : 'Inactive';
            echo "<p>- {$method['display_name']}: {$status}</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error testing functionality: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

echo "<hr>";
echo "<p><strong>Check completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>

<script>
function createTables() {
    const sql = document.querySelector('textarea').value;
    
    fetch('', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=create_tables&sql=' + encodeURIComponent(sql)
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('createResult').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('createResult').innerHTML = '<p style="color: red;">Error: ' + error + '</p>';
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
h3 { color: #34495e; margin-top: 20px; }
p { margin: 5px 0; }
.btn { padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; }
.btn:hover { background: #2980b9; }
textarea { border: 1px solid #ddd; padding: 10px; border-radius: 5px; }
</style>

<?php
// Handle table creation request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create_tables') {
    try {
        $sql = $_POST['sql'];
        
        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );
        
        $pdo->beginTransaction();
        
        $successCount = 0;
        $errors = [];
        
        foreach ($statements as $statement) {
            try {
                if (!empty(trim($statement))) {
                    $pdo->exec($statement);
                    $successCount++;
                }
            } catch (PDOException $e) {
                $errors[] = $e->getMessage();
            }
        }
        
        if (empty($errors)) {
            $pdo->commit();
            echo "<p style='color: green;'>✅ Successfully created $successCount tables/records!</p>";
            echo "<p><a href='{$_SERVER['PHP_SELF']}'>Refresh page to verify</a></p>";
        } else {
            $pdo->rollBack();
            echo "<p style='color: red;'>❌ Errors occurred:</p>";
            foreach ($errors as $error) {
                echo "<p style='color: red;'>• " . htmlspecialchars($error) . "</p>";
            }
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    exit;
}
?>

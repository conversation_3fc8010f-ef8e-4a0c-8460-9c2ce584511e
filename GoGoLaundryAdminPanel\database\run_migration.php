<?php
/**
 * Database Migration Runner
 * 
 * This script runs the enhanced shops management database migration
 * Run this script once to set up all required tables for the enhanced system
 */

// Include database connection
require_once '../includes/config.php';

try {
    echo "<h2>GoGoLaundry Enhanced Shops Management - Database Migration</h2>\n";
    echo "<p>Starting database migration...</p>\n";
    
    // Read the migration SQL file
    $migrationFile = __DIR__ . '/migrations/enhanced_shops_management.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read migration file");
    }
    
    echo "<p>Migration file loaded successfully.</p>\n";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "<p>Found " . count($statements) . " SQL statements to execute.</p>\n";
    
    $pdo->beginTransaction();
    
    $successCount = 0;
    $errors = [];
    
    foreach ($statements as $index => $statement) {
        try {
            // Skip comments and empty statements
            if (empty(trim($statement)) || preg_match('/^\s*--/', $statement)) {
                continue;
            }
            
            $pdo->exec($statement);
            $successCount++;
            echo "<p style='color: green;'>✓ Statement " . ($index + 1) . " executed successfully</p>\n";
            
        } catch (PDOException $e) {
            // Some statements might fail if tables/columns already exist - that's okay
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate') !== false) {
                echo "<p style='color: orange;'>⚠ Statement " . ($index + 1) . " skipped (already exists): " . htmlspecialchars($e->getMessage()) . "</p>\n";
            } else {
                $errors[] = "Statement " . ($index + 1) . ": " . $e->getMessage();
                echo "<p style='color: red;'>✗ Statement " . ($index + 1) . " failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
    }
    
    if (empty($errors)) {
        $pdo->commit();
        echo "<h3 style='color: green;'>✅ Migration completed successfully!</h3>\n";
        echo "<p>$successCount statements executed successfully.</p>\n";
        
        // Verify tables were created
        echo "<h4>Verifying created tables:</h4>\n";
        $tables = [
            'shop_admin_notes',
            'commission_logs', 
            'admin_activity_logs',
            'payment_withdrawal_requests',
            'payment_withdrawal_status_history',
            'shop_owner_earnings',
            'payment_method_settings'
        ];
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    echo "<p style='color: green;'>✓ Table '$table' exists</p>\n";
                } else {
                    echo "<p style='color: red;'>✗ Table '$table' not found</p>\n";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ Error checking table '$table': " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
        
        // Check if payment method settings were inserted
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM payment_method_settings");
            $count = $stmt->fetchColumn();
            echo "<p style='color: green;'>✓ Payment method settings: $count records</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error checking payment method settings: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
        
        echo "<hr>\n";
        echo "<h4>Next Steps:</h4>\n";
        echo "<ol>\n";
        echo "<li>The enhanced admin shops management page is now ready to use</li>\n";
        echo "<li>Access the admin panel at: <a href='../admin/shops.php'>../admin/shops.php</a></li>\n";
        echo "<li>Shop owners can now submit withdrawal requests</li>\n";
        echo "<li>Admins can manage withdrawal requests from the shops dashboard</li>\n";
        echo "</ol>\n";
        
    } else {
        $pdo->rollBack();
        echo "<h3 style='color: red;'>❌ Migration failed with errors:</h3>\n";
        foreach ($errors as $error) {
            echo "<p style='color: red;'>• " . htmlspecialchars($error) . "</p>\n";
        }
        echo "<p>Please fix the errors and run the migration again.</p>\n";
    }
    
} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "<h3 style='color: red;'>❌ Migration failed:</h3>\n";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check your database configuration and try again.</p>\n";
}

echo "<hr>\n";
echo "<p><strong>Migration completed at:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Migration - GoGoLaundry</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 20px auto; 
            padding: 20px; 
            line-height: 1.6;
        }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h3 { color: #333; }
        h4 { color: #666; margin-top: 20px; }
        p { margin: 5px 0; }
        hr { margin: 20px 0; border: 1px solid #ddd; }
        ol { margin-left: 20px; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <!-- Content is generated by PHP above -->
</body>
</html>
